<?xml version="1.0" encoding="utf-8"?>
<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
    <Metadata>
        <Identity Language="en-US" Id="mobile-forwarder-004" Version="1.0.0" Publisher="rescue-team" />
        <DisplayName>📱 手机转发004 - 项老师专用版</DisplayName>
        <Description xml:space="preserve">项老师专用手机消息转发器004，支持手机到VSCode Augment的无缝消息转发</Description>
        <Tags>救命,远程编程,手机消息,Augment转发,防鳄鱼</Tags>
        <Categories>Other,AI,Productivity</Categories>
        <GalleryFlags>Public</GalleryFlags>
        
        <Properties>
            <Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.82.0" />
            <Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="" />
            <Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
            <Property Id="Microsoft.VisualStudio.Code.ExtensionKind" Value="workspace" />
            <Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="" />
            <Property Id="Microsoft.VisualStudio.Code.EnabledApiProposals" Value="" />
            <Property Id="Microsoft.VisualStudio.Code.ExecutesCode" Value="true" />
            <Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
            <Property Id="Microsoft.VisualStudio.Services.Content.Pricing" Value="Free"/>
        </Properties>
        
    </Metadata>
    <Installation>
        <InstallationTarget Id="Microsoft.VisualStudio.Code"/>
    </Installation>
    <Dependencies/>
    <Assets>
        <Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
        <Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/readme.md" Addressable="true" />
    </Assets>
</PackageManifest>